const express = require("express");
const cors = require("cors");
const sqlite3 = require("sqlite3").verbose();
const path = require("path");
const fs = require("fs");

const app = express();
const PORT = 8002;

// Middleware
app.use(cors());
app.use(express.json());

// Database setup
const dbDir = path.join(__dirname, "..", "database");
const dbPath = path.join(dbDir, "habits.db");

// Create database directory if it doesn't exist
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

const db = new sqlite3.Database(dbPath);

// Initialize database tables
db.serialize(() => {
  // Create habits table
  db.run(`
        CREATE TABLE IF NOT EXISTS habits (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            color TEXT NOT NULL DEFAULT '#4A90E2',
            category TEXT NOT NULL DEFAULT 'DAILY',
            order_position INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    `);

  // Add order_position column if it doesn't exist (for existing databases)
  db.run(
    `
        ALTER TABLE habits ADD COLUMN order_position INTEGER DEFAULT 0
    `,
    (err) => {
      // Ignore error if column already exists
      if (err && !err.message.includes("duplicate column name")) {
        console.error("Error adding order_position column:", err);
      }
    }
  );

  // Add folder_id column to projects table if it doesn't exist (for existing databases)
  db.run(
    `
        ALTER TABLE projects ADD COLUMN folder_id INTEGER DEFAULT NULL
    `,
    (err) => {
      // Ignore error if column already exists
      if (err && !err.message.includes("duplicate column name")) {
        console.error("Error adding folder_id column:", err);
      }
    }
  );

  // Create habit_progress table
  db.run(`
        CREATE TABLE IF NOT EXISTS habit_progress (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            habit_id INTEGER NOT NULL,
            date DATE NOT NULL,
            completed BOOLEAN NOT NULL DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (habit_id) REFERENCES habits (id) ON DELETE CASCADE,
            UNIQUE(habit_id, date)
        )
    `);

  // Create projects table
  db.run(`
        CREATE TABLE IF NOT EXISTS projects (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            color TEXT DEFAULT '#E74C3C',
            category TEXT DEFAULT 'DAILY',
            type TEXT DEFAULT 'project',
            order_position INTEGER DEFAULT 0,
            total_time INTEGER DEFAULT 0,
            folder_id INTEGER DEFAULT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (folder_id) REFERENCES projects (id) ON DELETE SET NULL
        )
    `);

  // Create tasks table
  db.run(`
        CREATE TABLE IF NOT EXISTS tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            project_id INTEGER NOT NULL,
            name TEXT NOT NULL,
            completed BOOLEAN DEFAULT 0,
            time_spent INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
        )
    `);

  // Create pomodoro_sessions table
  db.run(`
        CREATE TABLE IF NOT EXISTS pomodoro_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            task_id INTEGER NOT NULL,
            project_id INTEGER NOT NULL,
            duration INTEGER NOT NULL,
            completed BOOLEAN DEFAULT 0,
            started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            completed_at DATETIME,
            FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE,
            FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
        )
    `);

  // Add project_id column to existing pomodoro_sessions table if it doesn't exist
  db.run(
    `ALTER TABLE pomodoro_sessions ADD COLUMN project_id INTEGER`,
    (err) => {
      // Ignore error if column already exists
      if (err && !err.message.includes("duplicate column name")) {
        console.error(
          "Error adding project_id column to pomodoro_sessions:",
          err
        );
      }
    }
  );

  // Create pomodoro_settings table
  db.run(`
        CREATE TABLE IF NOT EXISTS pomodoro_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT DEFAULT 'default',
            pomodoro_length INTEGER DEFAULT 25,
            short_break_length INTEGER DEFAULT 5,
            long_break_length INTEGER DEFAULT 15,
            long_break_after INTEGER DEFAULT 4,
            auto_start_pomodoro BOOLEAN DEFAULT 0,
            auto_start_break BOOLEAN DEFAULT 0,
            start_alarm TEXT DEFAULT 'beep',
            break_alarm TEXT DEFAULT 'beep',
            alarm_volume INTEGER DEFAULT 50,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(user_id)
        )
    `);

  // Create last_executed_task table
  db.run(`
        CREATE TABLE IF NOT EXISTS last_executed_task (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT DEFAULT 'default',
            task_id INTEGER,
            project_id INTEGER,
            task_name TEXT,
            project_name TEXT,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE SET NULL,
            FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE SET NULL,
            UNIQUE(user_id)
        )
    `);

  // Insert default habits if none exist
  db.get("SELECT COUNT(*) as count FROM habits", (err, row) => {
    if (err) {
      console.error("Error checking habits count:", err);
      return;
    }

    if (row.count === 0) {
      const defaultHabits = [
        { name: "Morning Water", color: "#316C96", category: "DAILY" },
        { name: "Clean", color: "#752EA8", category: "DAILY" },
        { name: "Workout", color: "#9D3B27", category: "DAILY" },
        { name: "Reading", color: "#2D9D65", category: "DAILY" },
        { name: "Journal", color: "#316C96", category: "DAILY" },
        { name: "Vitamins", color: "#B98941", category: "DAILY" },
      ];

      const stmt = db.prepare(
        "INSERT INTO habits (name, color, category) VALUES (?, ?, ?)"
      );
      defaultHabits.forEach((habit) => {
        stmt.run(habit.name, habit.color, habit.category);
      });
      stmt.finalize();
      console.log("Default habits inserted");
    }
  });
});

// Helper function to get habit progress
function getHabitProgress(habitId, callback) {
  db.all(
    "SELECT date, completed FROM habit_progress WHERE habit_id = ?",
    [habitId],
    (err, rows) => {
      if (err) {
        callback(err, null);
        return;
      }

      const progress = {};
      rows.forEach((row) => {
        progress[row.date] = Boolean(row.completed);
      });
      callback(null, progress);
    }
  );
}

// API Routes

// Database viewer endpoint (for debugging)
app.get("/api/database", (req, res) => {
  const results = {};

  // Get all habits
  db.all("SELECT * FROM habits ORDER BY created_at ASC", (err, habits) => {
    if (err) {
      console.error("Error fetching habits:", err);
      return res.status(500).json({ success: false, error: "Database error" });
    }

    results.habits = habits;

    // Get all progress
    db.all(
      "SELECT * FROM habit_progress ORDER BY habit_id, date",
      (err, progress) => {
        if (err) {
          console.error("Error fetching progress:", err);
          return res
            .status(500)
            .json({ success: false, error: "Database error" });
        }

        results.progress = progress;

        // Return formatted HTML for easy viewing
        const html = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>ANEW Database Viewer</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #fff; }
                        h1, h2 { color: #00ff88; }
                        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
                        th, td { border: 1px solid #555; padding: 8px; text-align: left; }
                        th { background-color: #333; }
                        tr:nth-child(even) { background-color: #2a2a2a; }
                        .json { background: #333; padding: 10px; border-radius: 5px; overflow-x: auto; }
                    </style>
                </head>
                <body>
                    <h1>🌱 ANEW Database Viewer</h1>

                    <h2>Habits Table</h2>
                    <table>
                        <tr><th>ID</th><th>Name</th><th>Color</th><th>Category</th><th>Created At</th></tr>
                        ${habits
                          .map(
                            (habit) => `
                            <tr>
                                <td>${habit.id}</td>
                                <td>${habit.name}</td>
                                <td><span style="background: ${habit.color}; padding: 2px 8px; border-radius: 3px; color: #000;">${habit.color}</span></td>
                                <td>${habit.category}</td>
                                <td>${habit.created_at}</td>
                            </tr>
                        `
                          )
                          .join("")}
                    </table>

                    <h2>Progress Table</h2>
                    <table>
                        <tr><th>ID</th><th>Habit ID</th><th>Date</th><th>Completed</th><th>Created At</th></tr>
                        ${progress
                          .map(
                            (p) => `
                            <tr>
                                <td>${p.id}</td>
                                <td>${p.habit_id}</td>
                                <td>${p.date}</td>
                                <td>${p.completed ? "✅" : "❌"}</td>
                                <td>${p.created_at}</td>
                            </tr>
                        `
                          )
                          .join("")}
                    </table>

                    <h2>Raw JSON Data</h2>
                    <div class="json">
                        <h3>Habits:</h3>
                        <pre>${JSON.stringify(habits, null, 2)}</pre>

                        <h3>Progress:</h3>
                        <pre>${JSON.stringify(progress, null, 2)}</pre>
                    </div>
                </body>
                </html>
            `;

        res.setHeader("Content-Type", "text/html");
        res.send(html);
      }
    );
  });
});

// Get all habits
app.get("/api/habits", (req, res) => {
  console.log("🔍 GET /api/habits - Request received");
  db.all(
    "SELECT * FROM habits ORDER BY order_position ASC, created_at ASC",
    (err, habits) => {
      if (err) {
        console.error("❌ Error fetching habits:", err);
        return res
          .status(500)
          .json({ success: false, error: "Database error" });
      }

      console.log("📋 Found", habits.length, "habits in database");
      console.log(
        "📋 Habits:",
        habits.map((h) => ({
          id: h.id,
          name: h.name,
          order_position: h.order_position,
        }))
      );

      // Get progress for each habit
      let completed = 0;
      const total = habits.length;

      if (total === 0) {
        console.log("📋 No habits found, returning empty array");
        return res.json({ success: true, habits: [] });
      }

      console.log("📋 Processing progress for", total, "habits");

      habits.forEach((habit, index) => {
        console.log(
          "📋 Getting progress for habit:",
          habit.name,
          "ID:",
          habit.id
        );
        getHabitProgress(habit.id, (err, progress) => {
          if (err) {
            console.error(
              "❌ Error fetching progress for habit",
              habit.id,
              ":",
              err
            );
            habit.progress = {};
          } else {
            console.log(
              "✅ Progress fetched for habit",
              habit.name,
              "- entries:",
              Object.keys(progress).length
            );
            habit.progress = progress;
          }

          completed++;
          console.log("📋 Progress completed:", completed, "/", total);
          if (completed === total) {
            console.log("✅ All progress fetched, sending response");
            res.json({ success: true, habits });
          }
        });
      });
    }
  );
});

// Add new habit
app.post("/api/habits", (req, res) => {
  const { name, color = "#2D9D65", category = "DAILY" } = req.body;

  if (!name || !name.trim()) {
    return res
      .status(400)
      .json({ success: false, error: "Habit name is required" });
  }

  const trimmedName = name.trim();

  // Check if habit with same name already exists (case-insensitive)
  db.get(
    "SELECT id FROM habits WHERE LOWER(name) = LOWER(?)",
    [trimmedName],
    (err, existingHabit) => {
      if (err) {
        console.error("Error checking for duplicate habit:", err);
        return res
          .status(500)
          .json({ success: false, error: "Database error" });
      }

      if (existingHabit) {
        return res.status(400).json({
          success: false,
          error: "A habit with this name already exists",
        });
      }

      // Insert new habit if no duplicate found
      db.run(
        "INSERT INTO habits (name, color, category) VALUES (?, ?, ?)",
        [trimmedName, color, category],
        function (err) {
          if (err) {
            console.error("Error adding habit:", err);
            return res
              .status(500)
              .json({ success: false, error: "Database error" });
          }

          // Get the newly created habit
          db.get(
            "SELECT * FROM habits WHERE id = ?",
            [this.lastID],
            (err, habit) => {
              if (err) {
                console.error("Error fetching new habit:", err);
                return res
                  .status(500)
                  .json({ success: false, error: "Database error" });
              }

              habit.progress = {};
              res.json({
                success: true,
                habit,
                message: "Habit added successfully",
              });
            }
          );
        }
      );
    }
  );
});

// Update habit order (must be before /:id route)
app.put("/api/habits/reorder", (req, res) => {
  console.log("🔄 REORDER endpoint called with body:", req.body);
  const { habitOrders } = req.body;

  if (!habitOrders || !Array.isArray(habitOrders)) {
    return res.status(400).json({
      success: false,
      error: "habitOrders array is required",
    });
  }

  // Start a transaction to update all habit orders
  db.serialize(() => {
    db.run("BEGIN TRANSACTION");

    let completed = 0;
    let hasError = false;

    habitOrders.forEach((item, index) => {
      if (hasError) return;

      const { id, order_position } = item;

      db.run(
        "UPDATE habits SET order_position = ? WHERE id = ?",
        [order_position, id],
        function (err) {
          if (err) {
            console.error("Error updating habit order:", err);
            hasError = true;
            db.run("ROLLBACK");
            return res.status(500).json({
              success: false,
              error: "Database error",
            });
          }

          completed++;
          if (completed === habitOrders.length) {
            db.run("COMMIT", (err) => {
              if (err) {
                console.error("Error committing transaction:", err);
                return res.status(500).json({
                  success: false,
                  error: "Database error",
                });
              }

              res.json({
                success: true,
                message: "Habit order updated successfully",
              });
            });
          }
        }
      );
    });
  });
});

// Update habit
app.put("/api/habits/:id", (req, res) => {
  const habitId = req.params.id;
  const { name, color, category } = req.body;

  if (!name || !name.trim()) {
    return res
      .status(400)
      .json({ success: false, error: "Habit name is required" });
  }

  const trimmedName = name.trim();

  // Check if another habit with the same name already exists (excluding current habit)
  db.get(
    "SELECT id FROM habits WHERE LOWER(name) = LOWER(?) AND id != ?",
    [trimmedName, habitId],
    (err, existingHabit) => {
      if (err) {
        console.error("Error checking for duplicate habit:", err);
        return res
          .status(500)
          .json({ success: false, error: "Database error" });
      }

      if (existingHabit) {
        return res.status(400).json({
          success: false,
          error: "A habit with this name already exists",
        });
      }

      // Update the habit
      db.run(
        "UPDATE habits SET name = ?, color = ?, category = ? WHERE id = ?",
        [trimmedName, color, category, habitId],
        function (err) {
          if (err) {
            console.error("Error updating habit:", err);
            return res
              .status(500)
              .json({ success: false, error: "Database error" });
          }

          if (this.changes === 0) {
            return res
              .status(404)
              .json({ success: false, error: "Habit not found" });
          }

          // Get the updated habit
          db.get(
            "SELECT * FROM habits WHERE id = ?",
            [habitId],
            (err, habit) => {
              if (err) {
                console.error("Error fetching updated habit:", err);
                return res
                  .status(500)
                  .json({ success: false, error: "Database error" });
              }

              res.json({
                success: true,
                habit,
                message: "Habit updated successfully",
              });
            }
          );
        }
      );
    }
  );
});

// Delete habit
app.delete("/api/habits/:id", (req, res) => {
  const habitId = req.params.id;

  // First delete all progress for this habit
  db.run("DELETE FROM habit_progress WHERE habit_id = ?", [habitId], (err) => {
    if (err) {
      console.error("Error deleting habit progress:", err);
      return res.status(500).json({ success: false, error: "Database error" });
    }

    // Then delete the habit itself
    db.run("DELETE FROM habits WHERE id = ?", [habitId], function (err) {
      if (err) {
        console.error("Error deleting habit:", err);
        return res
          .status(500)
          .json({ success: false, error: "Database error" });
      }

      if (this.changes === 0) {
        return res
          .status(404)
          .json({ success: false, error: "Habit not found" });
      }

      res.json({
        success: true,
        message: "Habit deleted successfully",
      });
    });
  });
});

// ===== PROJECT ENDPOINTS =====

// Get all projects
app.get("/api/projects", (req, res) => {
  console.log("🔍 GET /api/projects - Request received");
  // Only get projects that are not inside folders (folder_id is NULL) or are folders themselves
  db.all(
    "SELECT * FROM projects WHERE folder_id IS NULL ORDER BY category ASC, order_position ASC, created_at ASC",
    (err, projects) => {
      if (err) {
        console.error("Error fetching projects:", err);
        return res.status(500).json({
          success: false,
          error: "Database error",
        });
      }

      console.log("📋 Found", projects.length, "projects in database");
      console.log(
        "📋 Projects by category:",
        projects.reduce((acc, p) => {
          if (!acc[p.category]) acc[p.category] = [];
          acc[p.category].push({
            id: p.id,
            name: p.name,
            order_position: p.order_position,
          });
          return acc;
        }, {})
      );

      res.json({
        success: true,
        projects: projects,
      });
    }
  );
});

// Add new project
app.post("/api/projects", (req, res) => {
  const {
    name,
    color = "#E74C3C",
    category = "DAILY",
    type = "project",
  } = req.body;

  if (!name || !name.trim()) {
    return res
      .status(400)
      .json({ success: false, error: "Project name is required" });
  }

  const trimmedName = name.trim();

  // Check if project with same name already exists (case-insensitive)
  db.get(
    "SELECT id FROM projects WHERE LOWER(name) = LOWER(?)",
    [trimmedName],
    (err, existingProject) => {
      if (err) {
        console.error("Error checking existing project:", err);
        return res.status(500).json({
          success: false,
          error: "Database error",
        });
      }

      if (existingProject) {
        return res.status(400).json({
          success: false,
          error: "A project with this name already exists",
        });
      }

      // Get the highest order_position for this category
      db.get(
        "SELECT MAX(order_position) as max_order FROM projects WHERE category = ?",
        [category],
        (err, result) => {
          if (err) {
            console.error("Error getting max order position:", err);
            return res.status(500).json({
              success: false,
              error: "Database error",
            });
          }

          const nextOrderPosition = (result.max_order || -1) + 1;

          db.run(
            "INSERT INTO projects (name, color, category, type, order_position) VALUES (?, ?, ?, ?, ?)",
            [trimmedName, color, category, type, nextOrderPosition],
            function (err) {
              if (err) {
                console.error("Error creating project:", err);
                return res.status(500).json({
                  success: false,
                  error: "Database error",
                });
              }

              const newProject = {
                id: this.lastID,
                name: trimmedName,
                color,
                category,
                type,
                order_position: nextOrderPosition,
                created_at: new Date().toISOString(),
              };

              console.log("✅ Project created:", newProject);
              res.json({
                success: true,
                project: newProject,
              });
            }
          );
        }
      );
    }
  );
});

// Get single project by ID
app.get("/api/projects/:id", (req, res) => {
  const projectId = req.params.id;

  db.get("SELECT * FROM projects WHERE id = ?", [projectId], (err, project) => {
    if (err) {
      console.error("Error fetching project:", err);
      return res.status(500).json({
        success: false,
        error: "Database error",
      });
    }

    if (!project) {
      return res.status(404).json({
        success: false,
        error: "Project not found",
      });
    }

    res.json({
      success: true,
      project: project,
    });
  });
});

// Update project
app.put("/api/projects/:id", (req, res) => {
  const projectId = req.params.id;
  const { name, color, category, type } = req.body;

  if (name && !name.trim()) {
    return res
      .status(400)
      .json({ success: false, error: "Project name cannot be empty" });
  }

  // Build dynamic update query
  const updates = [];
  const values = [];

  if (name !== undefined) {
    updates.push("name = ?");
    values.push(name.trim());
  }
  if (color !== undefined) {
    updates.push("color = ?");
    values.push(color);
  }
  if (category !== undefined) {
    updates.push("category = ?");
    values.push(category);
  }
  if (type !== undefined) {
    updates.push("type = ?");
    values.push(type);
  }

  if (updates.length === 0) {
    return res.status(400).json({
      success: false,
      error: "No valid fields to update",
    });
  }

  values.push(projectId);

  db.run(
    `UPDATE projects SET ${updates.join(", ")} WHERE id = ?`,
    values,
    function (err) {
      if (err) {
        console.error("Error updating project:", err);
        return res.status(500).json({
          success: false,
          error: "Database error",
        });
      }

      if (this.changes === 0) {
        return res.status(404).json({
          success: false,
          error: "Project not found",
        });
      }

      // Get updated project
      db.get(
        "SELECT * FROM projects WHERE id = ?",
        [projectId],
        (err, project) => {
          if (err) {
            console.error("Error fetching updated project:", err);
            return res.status(500).json({
              success: false,
              error: "Database error",
            });
          }

          res.json({
            success: true,
            project: project,
          });
        }
      );
    }
  );
});

// Delete project
app.delete("/api/projects/:id", (req, res) => {
  const projectId = req.params.id;

  // First delete all tasks for this project
  db.run("DELETE FROM tasks WHERE project_id = ?", [projectId], (err) => {
    if (err) {
      console.error("Error deleting project tasks:", err);
      return res.status(500).json({
        success: false,
        error: "Database error",
      });
    }

    // Then delete the project
    db.run("DELETE FROM projects WHERE id = ?", [projectId], function (err) {
      if (err) {
        console.error("Error deleting project:", err);
        return res.status(500).json({
          success: false,
          error: "Database error",
        });
      }

      if (this.changes === 0) {
        return res.status(404).json({
          success: false,
          error: "Project not found",
        });
      }

      res.json({
        success: true,
        message: "Project deleted successfully",
      });
    });
  });
});

// Update project order
app.put("/api/projects/reorder", (req, res) => {
  console.log("🔄 PROJECT REORDER endpoint called with body:", req.body);
  const { projectOrders } = req.body;

  if (!projectOrders || !Array.isArray(projectOrders)) {
    return res.status(400).json({
      success: false,
      error: "projectOrders array is required",
    });
  }

  // Start a transaction to update all project orders
  db.serialize(() => {
    db.run("BEGIN TRANSACTION");

    let completed = 0;
    let hasError = false;

    projectOrders.forEach((item, index) => {
      if (hasError) return;

      const { id, order_position } = item;

      db.run(
        "UPDATE projects SET order_position = ? WHERE id = ?",
        [order_position, id],
        function (err) {
          if (err) {
            console.error("Error updating project order:", err);
            hasError = true;
            db.run("ROLLBACK");
            return res.status(500).json({
              success: false,
              error: "Database error",
            });
          }

          completed++;
          if (completed === projectOrders.length) {
            db.run("COMMIT", (err) => {
              if (err) {
                console.error("Error committing transaction:", err);
                return res.status(500).json({
                  success: false,
                  error: "Database error",
                });
              }

              res.json({
                success: true,
                message: "Project order updated successfully",
              });
            });
          }
        }
      );
    });
  });
});

// Move project to folder
app.put("/api/projects/:id/move-to-folder", (req, res) => {
  const projectId = req.params.id;
  const { folderId } = req.body;

  // Validate that the folder exists and is actually a folder
  if (folderId) {
    db.get(
      "SELECT id, type FROM projects WHERE id = ? AND type = 'folder'",
      [folderId],
      (err, folder) => {
        if (err) {
          console.error("Error checking folder:", err);
          return res.status(500).json({
            success: false,
            error: "Database error",
          });
        }

        if (!folder) {
          return res.status(400).json({
            success: false,
            error: "Folder not found or invalid",
          });
        }

        // Update the project's folder_id
        db.run(
          "UPDATE projects SET folder_id = ? WHERE id = ? AND type = 'project'",
          [folderId, projectId],
          function (err) {
            if (err) {
              console.error("Error moving project to folder:", err);
              return res.status(500).json({
                success: false,
                error: "Database error",
              });
            }

            if (this.changes === 0) {
              return res.status(404).json({
                success: false,
                error: "Project not found or is not a project",
              });
            }

            console.log(`✅ Project ${projectId} moved to folder ${folderId}`);
            res.json({
              success: true,
              message: "Project moved to folder successfully",
            });
          }
        );
      }
    );
  } else {
    // Remove project from folder (set folder_id to NULL)
    db.run(
      "UPDATE projects SET folder_id = NULL WHERE id = ? AND type = 'project'",
      [projectId],
      function (err) {
        if (err) {
          console.error("Error removing project from folder:", err);
          return res.status(500).json({
            success: false,
            error: "Database error",
          });
        }

        if (this.changes === 0) {
          return res.status(404).json({
            success: false,
            error: "Project not found or is not a project",
          });
        }

        console.log(`✅ Project ${projectId} removed from folder`);
        res.json({
          success: true,
          message: "Project removed from folder successfully",
        });
      }
    );
  }
});

// Get projects in a folder
app.get("/api/folders/:id/projects", (req, res) => {
  const folderId = req.params.id;

  // First verify the folder exists
  db.get(
    "SELECT id, name, type FROM projects WHERE id = ? AND type = 'folder'",
    [folderId],
    (err, folder) => {
      if (err) {
        console.error("Error checking folder:", err);
        return res.status(500).json({
          success: false,
          error: "Database error",
        });
      }

      if (!folder) {
        return res.status(404).json({
          success: false,
          error: "Folder not found",
        });
      }

      // Get all projects in this folder
      db.all(
        "SELECT * FROM projects WHERE folder_id = ? AND type = 'project' ORDER BY order_position ASC, created_at ASC",
        [folderId],
        (err, projects) => {
          if (err) {
            console.error("Error fetching folder projects:", err);
            return res.status(500).json({
              success: false,
              error: "Database error",
            });
          }

          console.log(
            `📁 Found ${projects.length} projects in folder ${folder.name}`
          );
          res.json({
            success: true,
            folder: folder,
            projects: projects,
          });
        }
      );
    }
  );
});

// Debug endpoint to check all projects
app.get("/api/debug/projects", (req, res) => {
  db.all(
    "SELECT id, name, type, category, folder_id FROM projects ORDER BY category, name",
    (err, projects) => {
      if (err) {
        console.error("Error fetching debug projects:", err);
        return res.status(500).json({
          success: false,
          error: "Failed to fetch projects",
        });
      }

      console.log("🔍 DEBUG: All projects in database:", projects);
      res.json({
        success: true,
        projects: projects,
      });
    }
  );
});

// ===== TASK ENDPOINTS =====

// Get tasks for a project
app.get("/api/projects/:id/tasks", (req, res) => {
  const projectId = req.params.id;

  db.all(
    "SELECT * FROM tasks WHERE project_id = ? ORDER BY created_at ASC",
    [projectId],
    (err, tasks) => {
      if (err) {
        console.error("Error fetching tasks:", err);
        return res.status(500).json({
          success: false,
          error: "Database error",
        });
      }

      res.json({
        success: true,
        tasks: tasks,
      });
    }
  );
});

// Add new task to project
app.post("/api/projects/:id/tasks", (req, res) => {
  const projectId = req.params.id;
  const { name } = req.body;

  if (!name || !name.trim()) {
    return res
      .status(400)
      .json({ success: false, error: "Task name is required" });
  }

  db.run(
    "INSERT INTO tasks (project_id, name) VALUES (?, ?)",
    [projectId, name.trim()],
    function (err) {
      if (err) {
        console.error("Error creating task:", err);
        return res.status(500).json({
          success: false,
          error: "Database error",
        });
      }

      const newTask = {
        id: this.lastID,
        project_id: projectId,
        name: name.trim(),
        completed: false,
        created_at: new Date().toISOString(),
      };

      res.json({
        success: true,
        task: newTask,
      });
    }
  );
});

// Update task
app.put("/api/tasks/:id", (req, res) => {
  const taskId = req.params.id;
  const { name, completed } = req.body;

  // Build dynamic update query
  const updates = [];
  const values = [];

  if (name !== undefined) {
    if (!name.trim()) {
      return res
        .status(400)
        .json({ success: false, error: "Task name cannot be empty" });
    }
    updates.push("name = ?");
    values.push(name.trim());
  }
  if (completed !== undefined) {
    updates.push("completed = ?");
    values.push(completed ? 1 : 0);
  }

  if (updates.length === 0) {
    return res.status(400).json({
      success: false,
      error: "No valid fields to update",
    });
  }

  values.push(taskId);

  db.run(
    `UPDATE tasks SET ${updates.join(", ")} WHERE id = ?`,
    values,
    function (err) {
      if (err) {
        console.error("Error updating task:", err);
        return res.status(500).json({
          success: false,
          error: "Database error",
        });
      }

      if (this.changes === 0) {
        return res.status(404).json({
          success: false,
          error: "Task not found",
        });
      }

      res.json({
        success: true,
        message: "Task updated successfully",
      });
    }
  );
});

// Delete task
app.delete("/api/tasks/:id", (req, res) => {
  const taskId = req.params.id;

  db.run("DELETE FROM tasks WHERE id = ?", [taskId], function (err) {
    if (err) {
      console.error("Error deleting task:", err);
      return res.status(500).json({
        success: false,
        error: "Database error",
      });
    }

    if (this.changes === 0) {
      return res.status(404).json({
        success: false,
        error: "Task not found",
      });
    }

    res.json({
      success: true,
      message: "Task deleted successfully",
    });
  });
});

// Fix project order positions (development endpoint)
app.post("/api/projects/fix-order", (req, res) => {
  console.log("🔧 Fixing project order positions...");

  db.all(
    "SELECT * FROM projects ORDER BY category ASC, id ASC",
    (err, projects) => {
      if (err) {
        console.error("Error fetching projects for fix:", err);
        return res
          .status(500)
          .json({ success: false, error: "Database error" });
      }

      // Group projects by category
      const projectsByCategory = {};
      projects.forEach((project) => {
        if (!projectsByCategory[project.category]) {
          projectsByCategory[project.category] = [];
        }
        projectsByCategory[project.category].push(project);
      });

      console.log("📋 Projects by category before fix:", projectsByCategory);

      // Update order positions for each category
      const updates = [];
      Object.keys(projectsByCategory).forEach((category) => {
        projectsByCategory[category].forEach((project, index) => {
          updates.push({ id: project.id, order_position: index });
        });
      });

      console.log("🔄 Order updates to apply:", updates);

      // Apply updates
      db.serialize(() => {
        db.run("BEGIN TRANSACTION");

        let completed = 0;
        let hasError = false;

        updates.forEach((update) => {
          if (hasError) return;

          db.run(
            "UPDATE projects SET order_position = ? WHERE id = ?",
            [update.order_position, update.id],
            function (err) {
              if (err) {
                console.error("Error updating project order:", err);
                hasError = true;
                db.run("ROLLBACK");
                return res
                  .status(500)
                  .json({ success: false, error: "Database error" });
              }

              completed++;
              if (completed === updates.length) {
                db.run("COMMIT", (err) => {
                  if (err) {
                    console.error("Error committing transaction:", err);
                    return res
                      .status(500)
                      .json({ success: false, error: "Database error" });
                  }

                  console.log("✅ Project order positions fixed successfully");
                  res.json({
                    success: true,
                    message: "Order positions fixed",
                    updates: updates.length,
                  });
                });
              }
            }
          );
        });
      });
    }
  );
});

// Recalculate project times from existing sessions (development endpoint)
app.post("/api/projects/recalculate-times", (req, res) => {
  console.log("🔧 Recalculating project times from existing sessions...");

  // Get all projects
  db.all("SELECT id, name FROM projects", (err, projects) => {
    if (err) {
      console.error("Error fetching projects:", err);
      return res.status(500).json({
        success: false,
        error: "Database error",
      });
    }

    if (projects.length === 0) {
      return res.json({
        success: true,
        message: "No projects found",
      });
    }

    let completedProjects = 0;
    const projectUpdates = [];

    projects.forEach((project) => {
      // Calculate total time for this project from sessions
      db.all(
        "SELECT SUM(duration) as total_duration FROM pomodoro_sessions WHERE project_id = ?",
        [project.id],
        (sessionErr, sessionResult) => {
          if (sessionErr) {
            console.error(
              `Error calculating time for project ${project.id}:`,
              sessionErr
            );
            completedProjects++;
          } else {
            const totalTime = sessionResult[0]?.total_duration || 0;

            // Update the project's total_time
            db.run(
              "UPDATE projects SET total_time = ? WHERE id = ?",
              [totalTime, project.id],
              (updateErr) => {
                if (updateErr) {
                  console.error(
                    `Error updating time for project ${project.id}:`,
                    updateErr
                  );
                } else {
                  console.log(
                    `✅ Updated project "${project.name}" total time to ${totalTime} seconds`
                  );
                  projectUpdates.push({
                    id: project.id,
                    name: project.name,
                    totalTime: totalTime,
                  });
                }

                completedProjects++;

                // Check if all projects are processed
                if (completedProjects === projects.length) {
                  res.json({
                    success: true,
                    message: `Recalculated times for ${projects.length} projects`,
                    updates: projectUpdates,
                  });
                }
              }
            );
          }
        }
      );
    });
  });
});

// Toggle habit progress
app.post("/api/progress", (req, res) => {
  const { habit_id, date } = req.body;

  if (!habit_id || !date) {
    return res
      .status(400)
      .json({ success: false, error: "Habit ID and date are required" });
  }

  // Validate date format
  if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
    return res
      .status(400)
      .json({ success: false, error: "Invalid date format. Use YYYY-MM-DD" });
  }

  // Check if progress exists for this date
  db.get(
    "SELECT completed FROM habit_progress WHERE habit_id = ? AND date = ?",
    [habit_id, date],
    (err, row) => {
      if (err) {
        console.error("Error checking progress:", err);
        return res
          .status(500)
          .json({ success: false, error: "Database error" });
      }

      if (row) {
        // Toggle existing progress
        const newCompleted = !row.completed;
        db.run(
          "UPDATE habit_progress SET completed = ? WHERE habit_id = ? AND date = ?",
          [newCompleted, habit_id, date],
          (err) => {
            if (err) {
              console.error("Error updating progress:", err);
              return res
                .status(500)
                .json({ success: false, error: "Database error" });
            }

            res.json({
              success: true,
              completed: newCompleted,
              message: "Progress updated successfully",
            });
          }
        );
      } else {
        // Create new progress entry as completed
        db.run(
          "INSERT INTO habit_progress (habit_id, date, completed) VALUES (?, ?, 1)",
          [habit_id, date],
          (err) => {
            if (err) {
              console.error("Error creating progress:", err);
              return res
                .status(500)
                .json({ success: false, error: "Database error" });
            }

            res.json({
              success: true,
              completed: true,
              message: "Progress updated successfully",
            });
          }
        );
      }
    }
  );
});

// ===== POMODORO ENDPOINTS =====

// Save pomodoro session
app.post("/api/pomodoro/sessions", (req, res) => {
  const { taskId, projectId, duration, completed, startedAt, completedAt } =
    req.body;

  if (!taskId || duration === undefined) {
    return res.status(400).json({
      success: false,
      error: "Task ID and duration are required",
    });
  }

  db.run(
    `INSERT INTO pomodoro_sessions (task_id, project_id, duration, completed, started_at, completed_at)
     VALUES (?, ?, ?, ?, ?, ?)`,
    [
      taskId,
      projectId || null,
      duration,
      completed || false,
      startedAt || new Date().toISOString(),
      completedAt,
    ],
    function (err) {
      if (err) {
        console.error("Error saving pomodoro session:", err);
        return res.status(500).json({
          success: false,
          error: "Database error",
        });
      }

      // Update task time_spent
      db.run(
        "UPDATE tasks SET time_spent = time_spent + ? WHERE id = ?",
        [duration, taskId],
        (updateErr) => {
          if (updateErr) {
            console.error("Error updating task time:", updateErr);
          }
        }
      );

      // Update project total_time (only if projectId is provided)
      if (projectId) {
        db.run(
          "UPDATE projects SET total_time = total_time + ? WHERE id = ?",
          [duration, projectId],
          (updateErr) => {
            if (updateErr) {
              console.error("Error updating project time:", updateErr);
            }
          }
        );
      }

      res.json({
        success: true,
        sessionId: this.lastID,
        message: "Pomodoro session saved successfully",
      });
    }
  );
});

// Get pomodoro sessions (for statistics)
app.get("/api/pomodoro/sessions", (req, res) => {
  const { startDate, endDate, taskId, projectId } = req.query;

  let query = `
    SELECT ps.*,
           t.name as task_name,
           COALESCE(p.name, 'Unknown Project') as project_name,
           COALESCE(p.color, '#666666') as project_color
    FROM pomodoro_sessions ps
    LEFT JOIN tasks t ON ps.task_id = t.id
    LEFT JOIN projects p ON ps.project_id = p.id
    WHERE 1=1
  `;
  const params = [];

  if (startDate) {
    query += " AND ps.started_at >= ?";
    params.push(startDate);
  }
  if (endDate) {
    query += " AND ps.started_at <= ?";
    params.push(endDate);
  }
  if (taskId) {
    query += " AND ps.task_id = ?";
    params.push(taskId);
  }
  if (projectId) {
    query += " AND ps.project_id = ?";
    params.push(projectId);
  }

  query += " ORDER BY ps.started_at DESC";

  db.all(query, params, (err, sessions) => {
    if (err) {
      console.error("Error fetching pomodoro sessions:", err);
      return res.status(500).json({
        success: false,
        error: "Database error",
      });
    }

    res.json({
      success: true,
      sessions: sessions,
    });
  });
});

// Get pomodoro settings
app.get("/api/pomodoro/settings", (req, res) => {
  const userId = req.query.userId || "default";

  db.get(
    "SELECT * FROM pomodoro_settings WHERE user_id = ?",
    [userId],
    (err, settings) => {
      if (err) {
        console.error("Error fetching pomodoro settings:", err);
        return res.status(500).json({
          success: false,
          error: "Database error",
        });
      }

      if (!settings) {
        // Return default settings if none exist
        const defaultSettings = {
          pomodoroLength: 25,
          shortBreakLength: 5,
          longBreakLength: 15,
          longBreakAfter: 4,
          autoStartPomodoro: false,
          autoStartBreak: false,
          startAlarm: "beep",
          breakAlarm: "beep",
          alarmVolume: 50,
        };
        return res.json({
          success: true,
          settings: defaultSettings,
        });
      }

      // Convert database format to frontend format
      const frontendSettings = {
        pomodoroLength: settings.pomodoro_length,
        shortBreakLength: settings.short_break_length,
        longBreakLength: settings.long_break_length,
        longBreakAfter: settings.long_break_after,
        autoStartPomodoro: !!settings.auto_start_pomodoro,
        autoStartBreak: !!settings.auto_start_break,
        startAlarm: settings.start_alarm,
        breakAlarm: settings.break_alarm,
        alarmVolume: settings.alarm_volume,
      };

      res.json({
        success: true,
        settings: frontendSettings,
      });
    }
  );
});

// Save pomodoro settings
app.put("/api/pomodoro/settings", (req, res) => {
  const userId = req.body.userId || "default";
  const {
    pomodoroLength,
    shortBreakLength,
    longBreakLength,
    longBreakAfter,
    autoStartPomodoro,
    autoStartBreak,
    startAlarm,
    breakAlarm,
    alarmVolume,
  } = req.body;

  db.run(
    `INSERT OR REPLACE INTO pomodoro_settings
     (user_id, pomodoro_length, short_break_length, long_break_length, long_break_after,
      auto_start_pomodoro, auto_start_break, start_alarm, break_alarm, alarm_volume, updated_at)
     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
    [
      userId,
      pomodoroLength || 25,
      shortBreakLength || 5,
      longBreakLength || 15,
      longBreakAfter || 4,
      autoStartPomodoro ? 1 : 0,
      autoStartBreak ? 1 : 0,
      startAlarm || "beep",
      breakAlarm || "beep",
      alarmVolume || 50,
      new Date().toISOString(),
    ],
    function (err) {
      if (err) {
        console.error("Error saving pomodoro settings:", err);
        return res.status(500).json({
          success: false,
          error: "Database error",
        });
      }

      res.json({
        success: true,
        message: "Settings saved successfully",
      });
    }
  );
});

// Get last executed task
app.get("/api/pomodoro/last-task", (req, res) => {
  const userId = req.query.userId || "default";

  db.get(
    "SELECT * FROM last_executed_task WHERE user_id = ?",
    [userId],
    (err, lastTask) => {
      if (err) {
        console.error("Error fetching last executed task:", err);
        return res.status(500).json({
          success: false,
          error: "Database error",
        });
      }

      res.json({
        success: true,
        lastTask: lastTask,
      });
    }
  );
});

// Save last executed task
app.put("/api/pomodoro/last-task", (req, res) => {
  const userId = req.body.userId || "default";
  const { taskId, projectId, taskName, projectName } = req.body;

  db.run(
    `INSERT OR REPLACE INTO last_executed_task
     (user_id, task_id, project_id, task_name, project_name, updated_at)
     VALUES (?, ?, ?, ?, ?, ?)`,
    [
      userId,
      taskId,
      projectId,
      taskName,
      projectName,
      new Date().toISOString(),
    ],
    function (err) {
      if (err) {
        console.error("Error saving last executed task:", err);
        return res.status(500).json({
          success: false,
          error: "Database error",
        });
      }

      res.json({
        success: true,
        message: "Last executed task saved successfully",
      });
    }
  );
});

// Start server
console.log("Starting server...");
app.listen(PORT, () => {
  console.log(`✅ Server running on http://localhost:${PORT}`);
  console.log("API endpoints available:");
  console.log("  === HABITS ===");
  console.log("  GET    /api/habits");
  console.log("  POST   /api/habits");
  console.log("  PUT    /api/habits/:id");
  console.log("  DELETE /api/habits/:id");
  console.log("  PUT    /api/habits/reorder");
  console.log("  POST   /api/progress");
  console.log("  === PROJECTS ===");
  console.log("  GET    /api/projects");
  console.log("  GET    /api/projects/:id");
  console.log("  POST   /api/projects");
  console.log("  PUT    /api/projects/:id");
  console.log("  DELETE /api/projects/:id");
  console.log("  PUT    /api/projects/reorder");
  console.log("  PUT    /api/projects/:id/move-to-folder");
  console.log("  GET    /api/folders/:id/projects");
  console.log("  === TASKS ===");
  console.log("  GET    /api/projects/:id/tasks");
  console.log("  POST   /api/projects/:id/tasks");
  console.log("  PUT    /api/tasks/:id");
  console.log("  DELETE /api/tasks/:id");
  console.log("  === POMODORO ===");
  console.log("  POST   /api/pomodoro/sessions");
  console.log("  GET    /api/pomodoro/sessions");
  console.log("  GET    /api/pomodoro/settings");
  console.log("  PUT    /api/pomodoro/settings");
  console.log("  GET    /api/pomodoro/last-task");
  console.log("  PUT    /api/pomodoro/last-task");
  console.log("  GET    /api/projects/:id/tasks");
  console.log("  POST   /api/projects/:id/tasks");
  console.log("  PUT    /api/tasks/:id");
  console.log("  DELETE /api/tasks/:id");
  console.log("  === OTHER ===");
  console.log("  GET    /api/database (Database viewer)");
});

// Graceful shutdown
process.on("SIGINT", () => {
  console.log("\nShutting down server...");
  db.close((err) => {
    if (err) {
      console.error("Error closing database:", err);
    } else {
      console.log("Database connection closed.");
    }
    process.exit(0);
  });
});

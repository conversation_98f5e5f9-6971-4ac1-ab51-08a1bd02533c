<template>
  <div
    class="project-card"
    @click="$emit('open-project', project)"
    :style="{ '--project-color': project.color }"
  >
    <div class="project-icon" :style="{ backgroundColor: project.color }">
      <!-- Folder icon for folders -->
      <svg v-if="project.type === 'folder'" viewBox="0 0 24 24" fill="white">
        <path
          d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z"
        />
      </svg>
      <!-- Circle for projects (empty circle) -->
      <div v-else class="project-circle"></div>
    </div>

    <div class="project-info">
      <div class="project-name" :style="{ color: project.color }">
        {{ project.name }}
      </div>
    </div>

    <div class="project-menu">
      <button
        class="menu-button"
        @click.stop="toggleMenu"
        :class="{ active: showMenu }"
      >
        <!-- Horizontal dots icon -->
        <svg width="60" height="60" viewBox="0 0 60 60" fill="currentColor">
          <circle cx="15" cy="30" r="5" />
          <circle cx="30" cy="30" r="5" />
          <circle cx="45" cy="30" r="5" />
        </svg>
      </button>

      <div v-if="showMenu" class="menu-dropdown" @click.stop>
        <button class="menu-item" @click="handleEdit">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"
            />
          </svg>
          Edit
        </button>
        <button class="menu-item" @click="handleComplete">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
          </svg>
          Complete
        </button>
        <button
          v-if="project.folder_id"
          class="menu-item"
          @click="handleRemoveFromFolder"
        >
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 13H5v-2h14v2z" />
          </svg>
          Remove from Folder
        </button>
        <button class="menu-item delete" @click="handleDelete">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"
            />
          </svg>
          Delete
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from "vue";

export default {
  name: "ProjectCard",
  props: {
    project: {
      type: Object,
      required: true,
    },
  },
  emits: [
    "open-project",
    "edit-project",
    "complete-project",
    "delete-project",
    "remove-from-folder",
  ],
  setup(props, { emit }) {
    const showMenu = ref(false);

    const toggleMenu = () => {
      showMenu.value = !showMenu.value;
    };

    const handleEdit = () => {
      showMenu.value = false;
      emit("edit-project", props.project);
    };

    const handleComplete = () => {
      showMenu.value = false;
      emit("complete-project", props.project.id);
    };

    const handleDelete = () => {
      showMenu.value = false;
      if (confirm(`Are you sure you want to delete "${props.project.name}"?`)) {
        emit("delete-project", props.project.id);
      }
    };

    const handleRemoveFromFolder = () => {
      showMenu.value = false;
      if (
        confirm(
          `Are you sure you want to remove "${props.project.name}" from its folder?`
        )
      ) {
        emit("remove-from-folder", props.project.id);
      }
    };

    const handleClickOutside = (event) => {
      if (showMenu.value && !event.target.closest(".project-menu")) {
        showMenu.value = false;
      }
    };

    onMounted(() => {
      document.addEventListener("click", handleClickOutside);
    });

    onUnmounted(() => {
      document.removeEventListener("click", handleClickOutside);
    });

    return {
      showMenu,
      toggleMenu,
      handleEdit,
      handleComplete,
      handleDelete,
      handleRemoveFromFolder,
    };
  },
};
</script>

<style scoped>
.project-card {
  background: #303030;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid #333;
  position: relative;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  min-height: 80px;
}

.project-card:hover {
  background: #333;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.project-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.project-icon svg {
  width: 24px;
  height: 24px;
}

.project-circle {
  width: 24px;
  height: 24px;
  border: 2px solid white;
  border-radius: 50%;
}

.project-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.project-name {
  font-size: 2rem;
  font-weight: 600;
  text-align: center;
}

.project-menu {
  position: relative;
}

.menu-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-button:hover,
.menu-button.active {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.menu-button svg {
  width: 40px;
  height: 40px;
}

.menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 0.5rem 0;
  min-width: 120px;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.5rem 1rem;
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.2s;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.menu-item.delete {
  color: #e74c3c;
}

.menu-item.delete:hover {
  background: rgba(231, 76, 60, 0.1);
}

.menu-item svg {
  width: 16px;
  height: 16px;
}
</style>

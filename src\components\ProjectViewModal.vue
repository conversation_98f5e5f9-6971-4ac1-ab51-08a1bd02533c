<template>
  <div class="modal-overlay" @click="$emit('close')">
    <div class="modal-content" @click.stop>
      <!-- Header -->
      <div class="modal-header">
        <h2>{{ project.name }}</h2>
        <button class="close-button" @click="$emit('close')">×</button>
      </div>

      <!-- Folder View: Show Projects -->
      <div v-if="project.type === 'folder'">
        <!-- Project Count Display -->
        <div class="project-count">
          <div class="count-display">
            <span class="count">{{ folderProjects.length }}</span>
            <span class="unit"
              >project{{ folderProjects.length !== 1 ? "s" : "" }}</span
            >
          </div>
          <div class="count-label">In Folder</div>
        </div>

        <!-- Add Project to Folder Section -->
        <div class="add-project-section">
          <div class="searchable-dropdown">
            <input
              type="text"
              v-model="projectSearchQuery"
              placeholder="Search and select project to add..."
              class="project-search-input"
              @focus="showProjectDropdown = true"
              @input="showProjectDropdown = true"
              @blur="setTimeout(() => (showProjectDropdown = false), 200)"
            />
            <div class="dropdown-toggle" @click="toggleDropdown">
              <svg
                viewBox="0 0 24 24"
                fill="currentColor"
                class="dropdown-arrow"
              >
                <path d="M7 10l5 5 5-5z" />
              </svg>
            </div>
            <div v-if="showProjectDropdown" class="dropdown-list">
              <div class="dropdown-search">
                <input
                  type="text"
                  v-model="projectSearchQuery"
                  placeholder="Search projects..."
                  class="search-input"
                  @click.stop
                />
              </div>
              <div
                v-if="filteredAvailableProjects.length > 0"
                class="dropdown-options"
              >
                <div
                  v-for="project in filteredAvailableProjects"
                  :key="project.id"
                  class="dropdown-item"
                  @click="selectProject(project)"
                >
                  <div
                    class="project-icon-small"
                    :style="{ backgroundColor: project.color }"
                  >
                    <div class="project-circle-small"></div>
                  </div>
                  <span class="project-name-dropdown">{{ project.name }}</span>
                </div>
              </div>
              <div v-else class="dropdown-empty">
                <span v-if="availableProjectsToAdd.length === 0"
                  >No projects available in this category</span
                >
                <span v-else>No projects match your search</span>
              </div>
            </div>
          </div>
          <button
            @click="addProjectToFolder"
            class="add-project-button"
            :disabled="!selectedProjectToAdd"
          >
            Add to Folder
          </button>
        </div>

        <!-- Projects in Folder List -->
        <div class="projects-list">
          <div
            v-for="folderProject in folderProjects"
            :key="folderProject.id"
            class="project-item"
          >
            <div
              class="project-icon"
              :style="{ backgroundColor: folderProject.color }"
            >
              <div class="project-circle"></div>
            </div>

            <div class="project-content">
              <span
                class="project-name"
                :style="{ color: folderProject.color }"
                >{{ folderProject.name }}</span
              >
            </div>

            <div class="project-actions">
              <button class="action-button" @click="openProject(folderProject)">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8 5v14l11-7z" />
                </svg>
              </button>
              <button
                class="action-button delete"
                @click="removeProjectFromFolder(folderProject.id)"
              >
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 13H5v-2h14v2z" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <div v-if="folderProjects.length === 0" class="empty-state">
          <p>
            No projects in this folder yet. Add projects from the dropdown
            above!
          </p>
        </div>
      </div>

      <!-- Project View: Show Tasks -->
      <div v-else>
        <!-- Elapsed Time Display -->
        <div class="elapsed-time">
          <div class="time-display">
            <span class="hours">{{ formatHours(totalProjectTime) }}</span>
            <span class="unit">h</span>
            <span class="minutes">{{ formatMinutes(totalProjectTime) }}</span>
            <span class="unit">m</span>
          </div>
          <div class="time-label">Elapsed Time</div>
        </div>

        <!-- Add Task Input -->
        <div class="add-task-section">
          <input
            type="text"
            v-model="newTaskName"
            placeholder="Add a task"
            class="add-task-input"
            @keyup.enter="addTask"
          />
        </div>

        <!-- Tasks List -->
        <div class="tasks-list">
          <div v-for="task in tasks" :key="task.id" class="task-item">
            <button class="play-button" @click="startPomodoro(task)">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M8 5v14l11-7z" />
              </svg>
            </button>

            <div class="task-content">
              <span class="task-name">{{ task.name }}</span>
            </div>

            <div class="task-actions">
              <button class="action-button" @click="editTask(task)">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path
                    d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"
                  />
                </svg>
              </button>
              <button class="action-button delete" @click="deleteTask(task.id)">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path
                    d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <div v-if="tasks.length === 0" class="empty-state">
          <p>No tasks yet. Add your first task above!</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, watch, computed } from "vue";

export default {
  name: "ProjectViewModal",
  props: {
    project: {
      type: Object,
      required: true,
    },
  },
  emits: ["close", "update-project", "start-timer"],
  setup(props, { emit }) {
    const tasks = ref([]);
    const newTaskName = ref("");

    // Folder-related data
    const folderProjects = ref([]);
    const availableProjectsToAdd = ref([]);
    const selectedProjectToAdd = ref("");
    const projectSearchQuery = ref("");
    const showProjectDropdown = ref(false);

    // Calculate total project time from database
    const totalProjectTime = ref(0);

    // Computed property for filtered available projects based on search
    const filteredAvailableProjects = computed(() => {
      if (!projectSearchQuery.value.trim()) {
        return availableProjectsToAdd.value;
      }
      return availableProjectsToAdd.value.filter((project) =>
        project.name
          .toLowerCase()
          .includes(projectSearchQuery.value.toLowerCase())
      );
    });

    const loadProjectTime = async () => {
      try {
        // First try to get time from the project's total_time field
        const projectResponse = await fetch(
          `/api/projects/${props.project.id}`
        );
        const projectData = await projectResponse.json();

        if (projectData.success && projectData.project.total_time) {
          // Convert seconds to minutes
          totalProjectTime.value = Math.floor(
            projectData.project.total_time / 60
          );
          return;
        }

        // Fallback: Calculate from pomodoro sessions
        const sessionsResponse = await fetch(
          `/api/pomodoro/sessions?projectId=${props.project.id}`
        );
        const sessionsData = await sessionsResponse.json();

        if (sessionsData.success) {
          const totalSeconds = sessionsData.sessions.reduce(
            (sum, session) => sum + session.duration,
            0
          );
          // Convert seconds to minutes
          totalProjectTime.value = Math.floor(totalSeconds / 60);
        } else {
          // Final fallback: localStorage
          const focusData = JSON.parse(
            localStorage.getItem("focusTimeData") || "[]"
          );
          const projectTime = focusData
            .filter(
              (entry) =>
                entry.projectId === props.project.id ||
                entry.projectName === props.project.name
            )
            .reduce((total, entry) => total + entry.duration, 0);

          // Convert seconds to minutes
          totalProjectTime.value = Math.floor(projectTime / 60);
        }
      } catch (error) {
        console.error("Error loading project time:", error);
        totalProjectTime.value = 0;
      }
    };

    // Load tasks for this project
    const loadTasks = async () => {
      try {
        const response = await fetch(`/api/projects/${props.project.id}/tasks`);
        const data = await response.json();
        if (data.success) {
          tasks.value = data.tasks;
        }
      } catch (error) {
        console.error("Error loading tasks:", error);
      }
    };

    // Add new task
    const addTask = async () => {
      if (!newTaskName.value.trim()) return;

      try {
        const response = await fetch(
          `/api/projects/${props.project.id}/tasks`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              name: newTaskName.value.trim(),
            }),
          }
        );
        const data = await response.json();
        if (data.success) {
          tasks.value.push(data.task);
          newTaskName.value = "";
        }
      } catch (error) {
        console.error("Error adding task:", error);
      }
    };

    // Edit task
    const editTask = async (task) => {
      const newName = prompt("Edit task name:", task.name);
      if (newName && newName.trim() !== task.name) {
        try {
          const response = await fetch(`/api/tasks/${task.id}`, {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              name: newName.trim(),
            }),
          });
          const data = await response.json();
          if (data.success) {
            const taskIndex = tasks.value.findIndex((t) => t.id === task.id);
            if (taskIndex !== -1) {
              tasks.value[taskIndex].name = newName.trim();
            }
          }
        } catch (error) {
          console.error("Error editing task:", error);
        }
      }
    };

    // Delete task
    const deleteTask = async (taskId) => {
      if (confirm("Are you sure you want to delete this task?")) {
        try {
          const response = await fetch(`/api/tasks/${taskId}`, {
            method: "DELETE",
          });
          const data = await response.json();
          if (data.success) {
            tasks.value = tasks.value.filter((t) => t.id !== taskId);
          }
        } catch (error) {
          console.error("Error deleting task:", error);
        }
      }
    };

    // Start pomodoro timer
    const startPomodoro = (task) => {
      emit("start-timer", {
        ...task,
        project_id: props.project.id,
        project_name: props.project.name,
      });
    };

    // Formatting functions
    const formatHours = (totalMinutes) => {
      return Math.floor(totalMinutes / 60)
        .toString()
        .padStart(2, "0");
    };

    const formatMinutes = (totalMinutes) => {
      return (totalMinutes % 60).toString().padStart(2, "0");
    };

    // Load projects in folder
    const loadFolderProjects = async () => {
      if (props.project.type !== "folder") return;

      try {
        const response = await fetch(
          `/api/folders/${props.project.id}/projects`
        );
        const data = await response.json();
        if (data.success) {
          folderProjects.value = data.projects;
        }
      } catch (error) {
        console.error("Error loading folder projects:", error);
      }
    };

    // Load available projects to add to folder
    const loadAvailableProjects = async () => {
      if (props.project.type !== "folder") return;

      console.log("🔍 Loading available projects for folder:", {
        folderId: props.project.id,
        folderName: props.project.name,
        folderType: props.project.type,
        folderCategory: props.project.category,
      });

      try {
        const response = await fetch("/api/projects");
        const data = await response.json();
        console.log("📋 All projects from API:", data.projects);

        if (data.success) {
          // Filter out projects that are already in folders and folders themselves
          // Also filter by the same category as the folder
          const filtered = data.projects.filter((project) => {
            const isProject = project.type === "project";
            const notInFolder = project.folder_id === null;
            const sameCategory = project.category === props.project.category;

            console.log(`🔍 Checking project "${project.name}":`, {
              type: project.type,
              isProject,
              folder_id: project.folder_id,
              notInFolder,
              category: project.category,
              sameCategory,
              passes: isProject && notInFolder && sameCategory,
            });

            return isProject && notInFolder && sameCategory;
          });

          console.log("✅ Filtered available projects:", filtered);
          availableProjectsToAdd.value = filtered;
        }
      } catch (error) {
        console.error("Error loading available projects:", error);
      }
    };

    // Add project to folder
    const addProjectToFolder = async () => {
      if (!selectedProjectToAdd.value) return;

      try {
        const response = await fetch(
          `/api/projects/${selectedProjectToAdd.value}/move-to-folder`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              folderId: props.project.id,
            }),
          }
        );

        const data = await response.json();
        if (data.success) {
          // Reload both lists
          await loadFolderProjects();
          await loadAvailableProjects();
          // Clear the selection and search
          selectedProjectToAdd.value = "";
          projectSearchQuery.value = "";
        }
      } catch (error) {
        console.error("Error adding project to folder:", error);
      }
    };

    // Remove project from folder
    const removeProjectFromFolder = async (projectId) => {
      if (
        confirm("Are you sure you want to remove this project from the folder?")
      ) {
        try {
          const response = await fetch(
            `/api/projects/${projectId}/move-to-folder`,
            {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                folderId: null,
              }),
            }
          );

          const data = await response.json();
          if (data.success) {
            // Reload both lists
            await loadFolderProjects();
            await loadAvailableProjects();
          }
        } catch (error) {
          console.error("Error removing project from folder:", error);
        }
      }
    };

    // Open project (emit event to parent)
    const openProject = (project) => {
      emit("close"); // Close current modal
      // The parent component should handle opening the project
      setTimeout(() => {
        emit("update-project", project);
      }, 100);
    };

    // Toggle dropdown visibility
    const toggleDropdown = () => {
      showProjectDropdown.value = !showProjectDropdown.value;
      if (showProjectDropdown.value) {
        projectSearchQuery.value = ""; // Clear search when opening
      }
    };

    // Select project from dropdown
    const selectProject = (project) => {
      selectedProjectToAdd.value = project.id;
      projectSearchQuery.value = project.name;
      showProjectDropdown.value = false;
    };

    // Watch for project changes and reload time
    watch(
      () => props.project.id,
      () => {
        if (props.project.type === "folder") {
          loadFolderProjects();
          loadAvailableProjects();
        } else {
          loadProjectTime();
        }
      }
    );

    onMounted(() => {
      if (props.project.type === "folder") {
        loadFolderProjects();
        loadAvailableProjects();
      } else {
        loadTasks();
        loadProjectTime();
      }
    });

    return {
      tasks,
      newTaskName,
      totalProjectTime,
      folderProjects,
      availableProjectsToAdd,
      selectedProjectToAdd,
      projectSearchQuery,
      showProjectDropdown,
      filteredAvailableProjects,
      toggleDropdown,
      selectProject,
      loadProjectTime,
      addTask,
      editTask,
      deleteTask,
      startPomodoro,
      addProjectToFolder,
      removeProjectFromFolder,
      openProject,
      formatHours,
      formatMinutes,
    };
  },
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #1a1a1a;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  color: white;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #333;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.elapsed-time {
  text-align: center;
  padding: 2rem;
  background: #2a2a2a;
  margin: 1.5rem;
  border-radius: 8px;
}

.time-display {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.hours,
.minutes {
  color: #e74c3c;
}

.unit {
  color: #888;
  font-size: 2rem;
}

.time-label {
  color: #888;
  font-size: 1rem;
}

.add-task-section {
  padding: 0 1.5rem;
  margin-bottom: 1rem;
}

.add-task-input {
  width: 100%;
  padding: 1rem;
  background: #2a2a2a;
  border: 1px solid #333;
  border-radius: 8px;
  color: white;
  font-size: 1rem;
}

.add-task-input:focus {
  outline: none;
  border-color: #2d9d65;
}

.tasks-list {
  padding: 0 1.5rem 1.5rem;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #2a2a2a;
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.play-button {
  background: none;
  border: 2px solid #e74c3c;
  color: #e74c3c;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.play-button:hover:not(:disabled) {
  background: #e74c3c;
  color: white;
}

.play-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.play-button svg {
  width: 16px;
  height: 16px;
}

.task-content {
  flex: 1;
}

.task-name {
  font-size: 1rem;
  color: white;
}

.task-actions {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: all 0.2s;
}

.action-button:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.action-button.delete:hover {
  color: #e74c3c;
  background: rgba(231, 76, 60, 0.1);
}

.action-button svg {
  width: 16px;
  height: 16px;
}

/* Folder-specific styles */
.project-count {
  text-align: center;
  padding: 2rem;
  background: #2a2a2a;
  margin: 1.5rem;
  border-radius: 8px;
}

.count-display {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.count {
  font-size: 2.5rem;
  font-weight: bold;
  color: white;
}

.count-label {
  color: #888;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.add-project-section {
  display: flex;
  gap: 1rem;
  padding: 0 1.5rem;
  margin-bottom: 1.5rem;
}

.searchable-dropdown {
  position: relative;
  flex: 1;
}

.project-search-input {
  width: 100%;
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 0.75rem 2.5rem 0.75rem 0.75rem;
  color: white;
  font-size: 1rem;
  box-sizing: border-box;
  cursor: pointer;
}

.project-search-input:focus {
  outline: none;
  border-color: #4a90e2;
}

.dropdown-toggle {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #888;
  transition: color 0.2s;
}

.dropdown-toggle:hover {
  color: #4a90e2;
}

.dropdown-arrow {
  width: 16px;
  height: 16px;
}

.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #2a2a2a;
  border: 1px solid #444;
  border-top: none;
  border-radius: 0 0 8px 8px;
  max-height: 250px;
  overflow: hidden;
  z-index: 1000;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.dropdown-search {
  padding: 0.5rem;
  border-bottom: 1px solid #444;
}

.search-input {
  width: 100%;
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 6px;
  padding: 0.5rem;
  color: white;
  font-size: 0.9rem;
  box-sizing: border-box;
}

.search-input:focus {
  outline: none;
  border-color: #4a90e2;
}

.dropdown-options {
  max-height: 180px;
  overflow-y: auto;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  cursor: pointer;
  gap: 0.5rem;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background: #3a3a3a;
}

.project-icon-small {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.project-circle-small {
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
}

.project-name-dropdown {
  color: white;
  font-size: 1rem;
}

.dropdown-empty {
  padding: 1rem;
  text-align: center;
  color: #888;
  font-style: italic;
}

.add-project-button {
  background: #4a90e2;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.add-project-button:hover:not(:disabled) {
  background: #357abd;
}

.add-project-button:disabled {
  background: #444;
  cursor: not-allowed;
}

.projects-list {
  padding: 0 1.5rem 1.5rem;
}

.project-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #2a2a2a;
  border-radius: 8px;
  margin-bottom: 0.75rem;
  transition: background 0.2s;
}

.project-item:hover {
  background: #333;
}

.project-icon {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.project-circle {
  width: 20px;
  height: 20px;
  border: 2px solid white;
  border-radius: 50%;
}

.project-content {
  flex: 1;
}

.project-name {
  font-size: 1rem;
  font-weight: 500;
}

.project-actions {
  display: flex;
  gap: 0.5rem;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: #888;
}

.empty-state p {
  margin: 0;
  font-size: 1rem;
}
</style>
